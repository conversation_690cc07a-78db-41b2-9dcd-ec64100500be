@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-ardent-50 text-eclipse-950;
  }
}

@layer components {
  /* ORA Button System */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 transform hover:scale-105;
  }

  .btn-primary {
    @apply btn bg-aura-900 text-white hover:bg-aura-800 focus:ring-aura-500 shadow-lg;
  }

  .btn-secondary {
    @apply btn bg-sunbeam-900 text-white hover:bg-sunbeam-800 focus:ring-sunbeam-500 shadow-lg;
  }

  .btn-accent {
    @apply btn bg-bliss-900 text-white hover:bg-bliss-800 focus:ring-bliss-500 shadow-lg;
  }

  .btn-outline {
    @apply btn border-2 border-aura-900 text-aura-900 hover:bg-aura-900 hover:text-white focus:ring-aura-500;
  }

  .btn-ghost {
    @apply btn text-eclipse-950 hover:bg-ardent-100 focus:ring-aura-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg;
  }

  .btn-disabled {
    @apply btn bg-gray-300 text-gray-500 cursor-not-allowed transform-none hover:scale-100;
  }

  /* ORA Card System */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 backdrop-blur-sm;
  }

  .card-elevated {
    @apply card shadow-lg border-0;
  }

  .card-glass {
    @apply bg-white/80 rounded-xl shadow-lg border border-white/20 p-6 backdrop-blur-md;
  }

  /* ORA Input System */
  .input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200;
  }

  .input-error {
    @apply input border-red-500 focus:ring-red-500;
  }

  /* ORA Chat System */
  .chat-message {
    @apply p-4 rounded-2xl mb-4 max-w-3xl shadow-sm;
  }

  .chat-message.user {
    @apply bg-gradient-to-r from-aura-100 to-sunbeam-100 ml-auto border border-aura-200;
  }

  .chat-message.assistant {
    @apply bg-gradient-to-r from-ardent-50 to-bliss-100 mr-auto border border-bliss-200;
  }

  /* ORA Emotion System */
  .emotion-badge {
    @apply inline-block px-3 py-1 text-xs font-medium rounded-full;
  }

  .emotion-high {
    @apply emotion-badge bg-aura-100 text-aura-900 border border-aura-200;
  }

  .emotion-medium {
    @apply emotion-badge bg-sunbeam-100 text-sunbeam-900 border border-sunbeam-200;
  }

  .emotion-low {
    @apply emotion-badge bg-bliss-100 text-bliss-900 border border-bliss-200;
  }

  /* ORA Dynamic Background */
  .dynamic-background {
    @apply fixed inset-0 -z-10 overflow-hidden;
  }

  .background-blob {
    @apply absolute rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob;
  }

  .background-blob:nth-child(2) {
    animation-delay: 2s;
  }

  .background-blob:nth-child(3) {
    animation-delay: 4s;
  }

  /* ORA Voice Interface */
  .voice-indicator {
    @apply relative;
  }

  .voice-pulse {
    @apply absolute inset-0 rounded-full bg-aura-500 opacity-75 animate-ping;
  }

  .voice-recording {
    @apply bg-gradient-to-r from-aura-500 to-sunbeam-500 text-white;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* ORA Gradient Utilities */
  .gradient-aura {
    @apply bg-gradient-to-r from-aura-900 to-aura-700;
  }

  .gradient-sunbeam {
    @apply bg-gradient-to-r from-sunbeam-900 to-sunbeam-700;
  }

  .gradient-bliss {
    @apply bg-gradient-to-r from-bliss-900 to-bliss-700;
  }

  .gradient-warm {
    @apply bg-gradient-to-br from-aura-100 via-sunbeam-100 to-bliss-100;
  }

  .gradient-cool {
    @apply bg-gradient-to-br from-ardent-50 via-bliss-100 to-sunbeam-100;
  }

  /* Emotion-based gradients */
  .emotion-joy {
    @apply bg-gradient-to-r from-sunbeam-200 to-bliss-200;
  }

  .emotion-calm {
    @apply bg-gradient-to-r from-ardent-50 to-bliss-100;
  }

  .emotion-excited {
    @apply bg-gradient-to-r from-aura-200 to-sunbeam-200;
  }

  .emotion-focused {
    @apply bg-gradient-to-r from-eclipse-900/10 to-aura-100;
  }
}

/* ORA Custom Animations */
@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
  }
  40%, 50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.33);
  }
}

@keyframes voice-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.pulse-ring {
  animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.voice-pulse-animation {
  animation: voice-pulse 2s ease-in-out infinite;
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Voice recording animation */
.recording-indicator {
  position: relative;
}

.recording-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid #852616; /* Aura color */
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 1.5s ease-out infinite;
}

/* Floating blob animations for dynamic background */
.floating-blob-1 {
  animation: blob 7s infinite;
}

.floating-blob-2 {
  animation: blob 7s infinite;
  animation-delay: 2s;
}

.floating-blob-3 {
  animation: blob 7s infinite;
  animation-delay: 4s;
}
